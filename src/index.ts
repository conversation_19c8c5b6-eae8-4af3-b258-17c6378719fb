import express from "express";
import bodyParser from "body-parser";

import cors from "cors";
import dotenv from "dotenv";
import {
  S3Client,
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

dotenv.config();

const AWS_REGION = "eu-west-3";
const AWS_ACCESS_KEY_ID = "********************";
const AWS_SECRET_ACCESS_KEY = "TvlaysliVG24E6/g0+Hz0UPk8mXZEGXceyFkZS3B";
const S3_BUCKET = "dsuploaded";

const app = express();

// const BASE_URL = "/uppy-backend-demo";

// app.use(
//   cors({
//     origin: "http://localhost:5173",
//     methods: ["GET", "POST", "PUT", "OPTIONS"],
//     allowedHeaders: ["Content-Type", "Authorization"],
//   })
// );

// app.use(express.json());
// app.use(bodyParser.json());

// const s3 = new S3Client({
//   region: AWS_REGION!,
//   credentials: {
//     accessKeyId: AWS_ACCESS_KEY_ID!,
//     secretAccessKey: AWS_SECRET_ACCESS_KEY!,
//   },
// });

// app.get(`${BASE_URL}`, async (req, res) => {
//   res.json({
//     message: "Hello from /uppy-backend-demo!",
//   });
// });

// // Step 1: Initiate multipart upload
// app.post(`${BASE_URL}/create-multipart-upload`, async (req, res) => {
//   const { filename, contentType } = req.body;
//   const command = new CreateMultipartUploadCommand({
//     Bucket: S3_BUCKET!,
//     Key: filename,
//     ContentType: contentType,
//   });
//   const response = await s3.send(command);
//   res.json({ uploadId: response.UploadId, key: filename });
// });

// // Step 2: Get signed URL for part
// app.post(`${BASE_URL}//sign-upload-part`, async (req, res) => {
//   const { key, uploadId, partNumber, contentType } = req.body;
//   const command = new UploadPartCommand({
//     Bucket: S3_BUCKET!,
//     Key: key,
//     UploadId: uploadId,
//     PartNumber: partNumber,
//     ContentLength: undefined,
//     //ContentType: contentType,
//   });
//   const signedUrl = await getSignedUrl(s3, command, { expiresIn: 3600 });
//   res.json({ url: signedUrl });
// });

// // Step 3: Complete multipart upload
// app.post(`${BASE_URL}/complete-multipart-upload`, async (req, res) => {
//   const { key, uploadId, parts } = req.body;
//   const command = new CompleteMultipartUploadCommand({
//     Bucket: S3_BUCKET!,
//     Key: key,
//     UploadId: uploadId,
//     MultipartUpload: {
//       Parts: parts,
//     },
//   });
//   const result = await s3.send(command);
//   res.json({ location: result.Location });
// });

// app.post(`${BASE_URL}/create-presigned-url`, async (req, res) => {
//   const { filename, contentType } = req.body;

//   const command = new PutObjectCommand({
//     Bucket: S3_BUCKET!,
//     Key: filename,
//     ContentType: contentType,
//   });

//   const url = await getSignedUrl(s3, command, { expiresIn: 300 });
//   res.json({ url });
// });

// app.post(`${BASE_URL}/uploader`, async (req, res) => {
//   // Extract metadata name and caption safely
//   let metaName = req.body.metadata?.name ?? "";
//   if (metaName === "") {
//     metaName = req.body.filename;
//   }

//   let metaCaption = req.body.metadata?.caption ?? "";

//   // Build tags array
//   let tagsArray = [`fileName=${metaName}`];
//   if (metaCaption) {
//     tagsArray.push(`fileDescription=${metaCaption}`);
//   }

//   // Join tags without trailing &
//   const combinedTags = tagsArray.join("&");

//   const params = {
//     Metadata: {
//       fileName: metaName,
//       caption: metaCaption,
//       uploadDateUTC: new Date().toISOString(),
//     },
//     Bucket: S3_BUCKET,
//     Key: `Account_Uploads/${Date.now()}-${req.body.filename}`,
//     ContentType: req.body.contentType,
//     Tagging: "random=random", // keep a placeholder tag, if needed for voodoo
//   };

//   const command = new PutObjectCommand({
//     Bucket: S3_BUCKET,
//     Key: params.Key,
//     Tagging: combinedTags,
//   });

//   const url = await getSignedUrl(s3, command, { expiresIn: 3600 });

//   res.status(200).json({
//     method: "PUT",
//     url,
//     fields: {},
//     headers: {
//       "x-amz-tagging": combinedTags,
//     },
//   });

//   // getSignedUrl("putObject", params, (err, url) => {
//   //   // get the pre-signed URL from AWS - if you alter this URL, it will fail
//   //   res.status(200).json({
//   //     // send info back to the client/front end
//   //     method: "put", // our upload method
//   //     url, // variable to hold the URL
//   //     fields: {}, // leave this an empty object
//   //     headers: { "x-amz-tagging": combinedTags }, // here we add the tags we created above
//   //   });
//   // });
// });

import companion from "@uppy/companion";

dotenv.config();

// Middleware
app.use(cors());
app.use(express.json());

// AWS S3 Configuration
const s3Client = new S3Client({
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ACCESS_KEY_ID,
    secretAccessKey: AWS_SECRET_ACCESS_KEY,
  },
});

// Uppy Companion Configuration
const companionOptions = {
  providerOptions: {
    s3: {
      getKey: (req, filename) => {
        const userId = req.user?.id || "anonymous";
        return `uploads/${userId}/${Date.now()}-${filename.replace(
          /\s+/g,
          "_"
        )}`;
      },
      bucket: S3_BUCKET,
      s3Client, // Pass the configured S3 client
    },
  },
  server: {
    host: new URL(COMPANION_URL).hostname,
    protocol: new URL(COMPANION_URL).protocol.replace(":", ""),
  },
  filePath: "./tmp",
  secret: COMPANION_SECRET,
  debug: NODE_ENV !== "production",
};

// Apply Uppy Companion middleware
app.use(companion.app(companionOptions));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok" });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err);
  res.status(500).json({ error: "Internal server error" });
});

const PORT = PORT || 3020;
app.listen(PORT, () => {
  console.log(`Companion server running on port ${PORT}`);
});

// app.listen(5005, () => console.log("Server on http://localhost:5005"));
