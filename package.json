{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "dev": "nodemon src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "false3.835.0", "@aws-sdk/s3-request-presigner": "false3.835.0", "@uppy/companion": "false5.8.0", "aws-sdk": "false2.1692.0", "cors": "false2.8.5", "dotenv": "false16.5.0", "express": "false5.1.0"}, "devDependencies": {"@types/cors": "false2.8.19", "@types/express": "false5.0.3", "@types/node": "false24.0.3", "nodemon": "false3.1.10", "ts-node": "false10.9.2", "typescript": "false5.8.3"}}